package com.sux.web.controller.place;

import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.enums.BusinessType;
import com.sux.system.domain.PlaceInfo;
import com.sux.system.service.IPlaceInfoService;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.common.core.page.TableDataInfo;

/**
 * 场地信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/place/info")
public class PlaceInfoController extends BaseController
{
    @Autowired
    private IPlaceInfoService placeInfoService;

    /**
     * 查询场地信息列表
     */
    @PreAuthorize("@ss.hasPermi('place:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PlaceInfo placeInfo)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoList(placeInfo);
        return getDataTable(list);
    }

    /**
     * 查询推荐场地信息列表
     */
    @GetMapping("/featured")
    public TableDataInfo featuredList(PlaceInfo placeInfo)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectFeaturedPlaceInfoList(placeInfo);
        return getDataTable(list);
    }

    /**
     * 查询活跃场地信息列表
     */
    @GetMapping("/active")
    public TableDataInfo activeList(PlaceInfo placeInfo)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectActivePlaceInfoList(placeInfo);
        return getDataTable(list);
    }

    /**
     * 导出场地信息列表
     */
    @PreAuthorize("@ss.hasPermi('place:info:export')")
    @Log(title = "场地信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PlaceInfo placeInfo)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoList(placeInfo);
        ExcelUtil<PlaceInfo> util = new ExcelUtil<PlaceInfo>(PlaceInfo.class);
        util.exportExcel(response, list, "场地信息数据");
    }

    /**
     * 获取场地信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('place:info:query')")
    @GetMapping(value = "/{placeId}")
    public AjaxResult getInfo(@PathVariable("placeId") Long placeId)
    {
        return success(placeInfoService.selectPlaceInfoByPlaceId(placeId));
    }

    /**
     * 获取场地详细信息（包含关联信息）
     */
    @GetMapping(value = "/detail/{placeId}")
    public AjaxResult getDetail(@PathVariable("placeId") Long placeId)
    {
        PlaceInfo placeInfo = placeInfoService.selectPlaceInfoDetailByPlaceId(placeId);
        if (placeInfo != null) {
            // 更新浏览次数
            placeInfoService.updatePlaceInfoViewCount(placeId);
        }
        return success(placeInfo);
    }

    /**
     * 新增场地信息
     */
    @PreAuthorize("@ss.hasPermi('place:info:add')")
    @Log(title = "场地信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PlaceInfo placeInfo)
    {
        return toAjax(placeInfoService.insertPlaceInfo(placeInfo));
    }

    /**
     * 修改场地信息
     */
    @PreAuthorize("@ss.hasPermi('place:info:edit')")
    @Log(title = "场地信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PlaceInfo placeInfo)
    {
        return toAjax(placeInfoService.updatePlaceInfo(placeInfo));
    }

    /**
     * 删除场地信息
     */
    @PreAuthorize("@ss.hasPermi('place:info:remove')")
    @Log(title = "场地信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{placeIds}")
    public AjaxResult remove(@PathVariable Long[] placeIds)
    {
        return toAjax(placeInfoService.deletePlaceInfoByPlaceIds(placeIds));
    }

    /**
     * 根据场地类型查询场地信息列表
     */
    @GetMapping("/type/{placeType}")
    public AjaxResult getByType(@PathVariable String placeType)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByType(placeType);
        return success(list);
    }

    /**
     * 根据区域代码查询场地信息列表
     */
    @GetMapping("/region/{regionCode}")
    public AjaxResult getByRegion(@PathVariable String regionCode)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByRegion(regionCode);
        return success(list);
    }

    /**
     * 根据场地等级查询场地信息列表
     */
    @GetMapping("/level/{placeLevel}")
    public AjaxResult getByLevel(@PathVariable String placeLevel)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByLevel(placeLevel);
        return success(list);
    }

    /**
     * 根据行业方向查询场地信息列表
     */
    @GetMapping("/industry/{industryDirection}")
    public AjaxResult getByIndustry(@PathVariable String industryDirection)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByIndustry(industryDirection);
        return success(list);
    }

    /**
     * 根据运营模式查询场地信息列表
     */
    @GetMapping("/operation/{operationMode}")
    public AjaxResult getByOperationMode(@PathVariable String operationMode)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByOperationMode(operationMode);
        return success(list);
    }

    /**
     * 查询场地统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = placeInfoService.selectPlaceInfoStatistics();
        return success(statistics);
    }

    /**
     * 根据关键词搜索场地信息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 查询即将到期的招商场地
     */
    @GetMapping("/expiring")
    public AjaxResult getExpiringSoon(@RequestParam(defaultValue = "30") Integer days)
    {
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoExpiringSoon(days);
        return success(list);
    }

    /**
     * 获取所有场地类型列表
     */
    @GetMapping("/types")
    public AjaxResult getAllPlaceTypes()
    {
        List<String> types = placeInfoService.selectAllPlaceTypes();
        return success(types);
    }

    /**
     * 获取所有场地等级列表
     */
    @GetMapping("/levels")
    public AjaxResult getAllPlaceLevels()
    {
        List<String> levels = placeInfoService.selectAllPlaceLevels();
        return success(levels);
    }

    /**
     * 获取所有区域列表
     */
    @GetMapping("/regions")
    public AjaxResult getAllRegions()
    {
        List<Map<String, String>> regions = placeInfoService.selectAllRegions();
        return success(regions);
    }

    /**
     * 获取所有行业方向列表
     */
    @GetMapping("/industries")
    public AjaxResult getAllIndustryDirections()
    {
        List<String> industries = placeInfoService.selectAllIndustryDirections();
        return success(industries);
    }

    /**
     * 获取所有运营模式列表
     */
    @GetMapping("/operations")
    public AjaxResult getAllOperationModes()
    {
        List<String> operations = placeInfoService.selectAllOperationModes();
        return success(operations);
    }
}

<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>场地信息-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202503281048" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

    <style>
        /* 场地信息卡片样式 */
        .place-card {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e6e6e6;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .place-card:hover {
            border-color: #0052d9;
            box-shadow: 0 4px 16px rgba(0,82,217,0.15);
            transform: translateY(-2px);
        }

        .place-card.featured {
            border-color: #28a745;
            background: linear-gradient(135deg, #fff 0%, #f8fff9 100%);
        }

        .featured-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .place-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
            flex: 1;
            margin-right: 15px;
        }

        .place-level {
            font-size: 14px;
            font-weight: bold;
            color: #ff6000;
            background: #fff5f0;
            padding: 6px 12px;
            border-radius: 6px;
            white-space: nowrap;
        }

        .place-meta-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
        }

        .meta-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
        }

        .type-tag {
            background: #e3f2fd;
            color: #1976d2;
        }

        .region-tag {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .industry-tag {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .info-value {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .place-address {
            font-size: 12px;
            color: #999;
        }

        .view-count {
            font-size: 12px;
            color: #999;
        }

        .detail-btn {
            padding: 8px 16px;
            background: #0052d9;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .detail-btn:hover {
            background: #003d99;
            transform: translateY(-1px);
        }

        /* 筛选区域样式 */
        .filter-section {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-item label {
            font-size: 14px;
            color: #333;
            white-space: nowrap;
        }

        .filter-item select, .filter-item input {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        /* 统计信息样式 */
        .stats-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 加载和无数据状态 */
        .loading-state {
            text-align: center;
            padding: 40px;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0052d9;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-data-tip {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            margin: 40px auto;
            max-width: 400px;
        }

        .no-data-tip .icon {
            font-size: 64px;
            color: #e0e0e0;
            margin-bottom: 24px;
            line-height: 1;
        }

        .no-data-tip h3 {
            color: #666;
            margin-bottom: 12px;
            font-size: 20px;
            font-weight: 500;
        }

        .no-data-tip p {
            color: #999;
            font-size: 14px;
            line-height: 1.5;
            margin: 0;
        }
    </style>
</head>

<body>
    <div id="headerBar"></div>
    
    <!-- 主要内容区域 -->
    <div class="conAuto2" style="padding: 20px 0;">
        <!-- 页面标题 -->
        <div style="margin-bottom: 30px;">
            <h1 style="font-size: 28px; color: #333; margin-bottom: 10px;">场地信息</h1>
            <p style="color: #666; font-size: 16px;">为您提供优质的创业场地信息</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section" id="statsSection" style="display: none;">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalCount">0</div>
                    <div class="stat-label">场地总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalArea">0</div>
                    <div class="stat-label">总面积(㎡)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalUsableArea">0</div>
                    <div class="stat-label">可用面积(㎡)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalCompanies">0</div>
                    <div class="stat-label">入驻企业</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <div class="filter-item">
                    <label>关键词：</label>
                    <input type="text" id="keywordFilter" placeholder="请输入场地名称或地址" style="width: 200px;">
                </div>
                <div class="filter-item">
                    <label>场地类型：</label>
                    <select id="typeFilter">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>场地等级：</label>
                    <select id="levelFilter">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>区域：</label>
                    <select id="regionFilter">
                        <option value="">全部</option>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <div class="filter-item">
                    <label>行业方向：</label>
                    <select id="industryFilter">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>运营模式：</label>
                    <select id="operationFilter">
                        <option value="">全部</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label>&nbsp;</label>
                    <button class="detail-btn" onclick="searchPlaces()" style="background: #28a745;">搜索</button>
                    <button class="detail-btn" onclick="resetFilters()" style="background: #6c757d; margin-left: 10px;">重置</button>
                </div>
            </div>
        </div>

        <!-- 场地信息列表 -->
        <div class="placeList" id="placeList">
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state" style="display: none;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 15px; color: #666;">正在加载场地信息...</p>
            </div>

            <!-- 场地信息网格 -->
            <div id="placeGrid" style="display: none;">
                <!-- 场地信息卡片将在这里动态生成 -->
            </div>

            <!-- 无数据提示 -->
            <div id="noDataTip" class="no-data-tip" style="display: none;">
                <div class="icon">🏢</div>
                <h3>暂无场地信息</h3>
                <p>请调整筛选条件或稍后再试</p>
            </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container" id="placePagination"></div>
    </div>

    <!-- 底部 -->
    <div id="footerBar"></div>

    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8"></script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>

    <script>
        // 全局变量
        var placeList = [];
        var searchParams = {
            keyword: '',
            placeType: '',
            placeLevel: '',
            regionCode: '',
            industryDirection: '',
            operationMode: ''
        };

        // 页面初始化
        $(document).ready(function() {
            loadFilterOptions();
            loadStatistics();
            loadPlaceList();
        });

        // Ajax请求函数
        function ajaxRequest(url, params, callback) {
            var baseUrl = 'http://localhost:80/sux-admin/';
            
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var fullUrl = baseUrl + url + queryString;
            console.log('发送请求到:', fullUrl);

            $.ajax({
                url: fullUrl,
                type: 'GET',
                timeout: 30000,
                success: function(response) {
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('请求失败:', error);
                    if (callback && typeof callback === 'function') {
                        callback({code: -1, msg: '请求失败'});
                    }
                }
            });
        }

        // 加载筛选选项
        function loadFilterOptions() {
            // 加载场地类型
            ajaxRequest('public/place/types', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var typeSelect = $('#typeFilter');
                    data.data.forEach(function(type) {
                        typeSelect.append('<option value="' + type + '">' + type + '</option>');
                    });
                }
            });

            // 加载场地等级
            ajaxRequest('public/place/levels', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var levelSelect = $('#levelFilter');
                    data.data.forEach(function(level) {
                        levelSelect.append('<option value="' + level + '">' + level + '</option>');
                    });
                }
            });

            // 加载区域信息
            ajaxRequest('public/place/regions', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var regionSelect = $('#regionFilter');
                    data.data.forEach(function(region) {
                        regionSelect.append('<option value="' + region.regionCode + '">' + region.regionName + '</option>');
                    });
                }
            });

            // 加载行业方向
            ajaxRequest('public/place/industries', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var industrySelect = $('#industryFilter');
                    data.data.forEach(function(industry) {
                        industrySelect.append('<option value="' + industry + '">' + industry + '</option>');
                    });
                }
            });

            // 加载运营模式
            ajaxRequest('public/place/operations', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var operationSelect = $('#operationFilter');
                    data.data.forEach(function(operation) {
                        operationSelect.append('<option value="' + operation + '">' + operation + '</option>');
                    });
                }
            });
        }

        // 加载统计信息
        function loadStatistics() {
            ajaxRequest('public/place/statistics', {}, function(data) {
                if (data.code === 200 && data.data) {
                    var stats = data.data;
                    $('#totalCount').text(stats.total_count || 0);
                    $('#totalArea').text((stats.total_area || 0).toLocaleString());
                    $('#totalUsableArea').text((stats.total_usable_area || 0).toLocaleString());
                    $('#totalCompanies').text(stats.total_companies || 0);
                    $('#statsSection').show();
                }
            });
        }

        // 显示加载状态
        function showLoading() {
            $('#loadingState').show();
            $('#placeGrid').hide();
            $('#noDataTip').hide();
        }

        // 隐藏加载状态
        function hideLoading() {
            $('#loadingState').hide();
        }

        // 加载场地信息列表
        function loadPlaceList() {
            showLoading();

            var params = {
                pageSize: 10,
                pageNum: 1
            };

            // 添加筛选条件
            if (searchParams.keyword) params.keyword = searchParams.keyword;
            if (searchParams.placeType) params.placeType = searchParams.placeType;
            if (searchParams.placeLevel) params.placeLevel = searchParams.placeLevel;
            if (searchParams.regionCode) params.regionCode = searchParams.regionCode;
            if (searchParams.industryDirection) params.industryDirection = searchParams.industryDirection;
            if (searchParams.operationMode) params.operationMode = searchParams.operationMode;

            ajaxRequest('public/place/filter', params, function(data) {
                hideLoading();
                
                if (data.code === 200 && data.rows) {
                    placeList = data.rows;
                    renderPlaceList(data.rows);
                } else {
                    renderPlaceList([]);
                }
            });
        }

        // 渲染场地信息列表
        function renderPlaceList(places) {
            var placeGrid = $('#placeGrid');
            var noDataTip = $('#noDataTip');

            if (!places || places.length === 0) {
                placeGrid.hide();
                noDataTip.show();
                return;
            }

            var html = '';
            places.forEach(function(place) {
                var featuredClass = place.isFeatured === 1 ? 'place-card featured' : 'place-card';
                var featuredBadge = place.isFeatured === 1 ? '<div class="featured-badge">推荐</div>' : '';

                html += `
                    <div class="${featuredClass}" onclick="showPlaceDetail(${place.placeId})">
                        ${featuredBadge}
                        <!-- 卡片头部 -->
                        <div class="card-header">
                            <h3 class="place-title" title="${place.placeName || ''}">${place.placeName || '--'}</h3>
                            <div class="place-level">${place.placeLevel || '--'}</div>
                        </div>

                        <!-- 标签 -->
                        <div class="place-meta-tags">
                            <span class="meta-tag type-tag">${place.placeType || '--'}</span>
                            <span class="meta-tag region-tag">${place.regionName || '--'}</span>
                            <span class="meta-tag industry-tag">${place.industryDirection || '--'}</span>
                        </div>

                        <!-- 信息网格 -->
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-icon">📐</div>
                                <div class="info-content">
                                    <div class="info-label">可用面积</div>
                                    <div class="info-value">${place.usableArea || 0}㎡</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">🏢</div>
                                <div class="info-content">
                                    <div class="info-label">入驻企业</div>
                                    <div class="info-value">${place.companyCount || 0}家</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">💰</div>
                                <div class="info-content">
                                    <div class="info-label">租金范围</div>
                                    <div class="info-value">${place.rentPriceMin || 0}-${place.rentPriceMax || 0}元/㎡</div>
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-icon">⚙️</div>
                                <div class="info-content">
                                    <div class="info-label">运营模式</div>
                                    <div class="info-value">${place.operationMode || '--'}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 卡片底部 -->
                        <div class="card-footer">
                            <div class="place-address" title="${place.address || ''}">${place.address || '--'}</div>
                            <div style="display: flex; gap: 15px; align-items: center;">
                                <span class="view-count">浏览 ${place.viewCount || 0} 次</span>
                                <button class="detail-btn" onclick="event.stopPropagation(); showPlaceDetail(${place.placeId})">查看详情</button>
                            </div>
                        </div>
                    </div>
                `;
            });

            placeGrid.html(html);
            placeGrid.show();
            noDataTip.hide();
        }

        // 搜索场地
        function searchPlaces() {
            searchParams.keyword = $('#keywordFilter').val();
            searchParams.placeType = $('#typeFilter').val();
            searchParams.placeLevel = $('#levelFilter').val();
            searchParams.regionCode = $('#regionFilter').val();
            searchParams.industryDirection = $('#industryFilter').val();
            searchParams.operationMode = $('#operationFilter').val();

            loadPlaceList();
        }

        // 重置筛选条件
        function resetFilters() {
            $('#keywordFilter').val('');
            $('#typeFilter').val('');
            $('#levelFilter').val('');
            $('#regionFilter').val('');
            $('#industryFilter').val('');
            $('#operationFilter').val('');

            searchParams = {
                keyword: '',
                placeType: '',
                placeLevel: '',
                regionCode: '',
                industryDirection: '',
                operationMode: ''
            };

            loadPlaceList();
        }

        // 显示场地详情
        function showPlaceDetail(placeId) {
            // 这里可以跳转到详情页面或显示详情弹框
            window.open('./placeDetail.html?id=' + placeId, '_blank');
        }

        // 回车搜索
        $('#keywordFilter').keypress(function(e) {
            if (e.which === 13) {
                searchPlaces();
            }
        });
    </script>
</body>

</html>

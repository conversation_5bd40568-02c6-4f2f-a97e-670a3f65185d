package com.sux.web.controller.public;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.EmploymentInfo;
import com.sux.system.service.IEmploymentInfoService;

/**
 * 用工信息公开接口Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/employment")
public class PublicEmploymentController extends BaseController
{
    @Autowired
    private IEmploymentInfoService employmentInfoService;

    /**
     * 查询用工信息列表（公开接口）
     */
    @GetMapping("/list")
    public TableDataInfo list(EmploymentInfo employmentInfo)
    {
        startPage();
        // 只查询已发布的用工信息
        employmentInfo.setStatus("published");
        List<EmploymentInfo> list = employmentInfoService.selectPublishedEmploymentInfoList(employmentInfo);
        return getDataTable(list);
    }

    /**
     * 获取用工信息详细信息（公开接口）
     */
    @GetMapping(value = "/{employmentId}")
    public AjaxResult getInfo(@PathVariable("employmentId") Long employmentId)
    {
        EmploymentInfo employmentInfo = employmentInfoService.selectEmploymentInfoDetailByEmploymentId(employmentId);
        if (employmentInfo != null && "published".equals(employmentInfo.getStatus())) {
            // 增加浏览次数
            employmentInfoService.updateEmploymentInfoViewCount(employmentId);
            return success(employmentInfo);
        }
        return error("用工信息不存在或已下线");
    }

    /**
     * 获取用工统计信息（公开接口）
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = employmentInfoService.selectEmploymentInfoStatistics();
        return success(statistics);
    }

    /**
     * 获取推荐用工信息列表（公开接口）
     */
    @GetMapping("/featured")
    public AjaxResult getFeaturedList()
    {
        EmploymentInfo employmentInfo = new EmploymentInfo();
        employmentInfo.setStatus("published");
        employmentInfo.setIsFeatured(1);
        List<EmploymentInfo> list = employmentInfoService.selectFeaturedEmploymentInfoList(employmentInfo);
        return success(list);
    }

    /**
     * 根据用工类型查询用工信息（公开接口）
     */
    @GetMapping("/type/{employmentType}")
    public TableDataInfo getByType(@PathVariable("employmentType") String employmentType)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByType(employmentType);
        return getDataTable(list);
    }

    /**
     * 根据工作类别查询用工信息（公开接口）
     */
    @GetMapping("/category/{workCategory}")
    public TableDataInfo getByCategory(@PathVariable("workCategory") String workCategory)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByCategory(workCategory);
        return getDataTable(list);
    }

    /**
     * 根据区域查询用工信息（公开接口）
     */
    @GetMapping("/region/{regionCode}")
    public TableDataInfo getByRegion(@PathVariable("regionCode") String regionCode)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByRegion(regionCode);
        return getDataTable(list);
    }

    /**
     * 根据薪资类型查询用工信息（公开接口）
     */
    @GetMapping("/salary/{salaryType}")
    public TableDataInfo getBySalaryType(@PathVariable("salaryType") String salaryType)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoBySalaryType(salaryType);
        return getDataTable(list);
    }

    /**
     * 根据薪资范围查询用工信息（公开接口）
     */
    @GetMapping("/salary/range")
    public TableDataInfo getBySalaryRange(Integer minSalary, Integer maxSalary)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoBySalaryRange(minSalary, maxSalary);
        return getDataTable(list);
    }

    /**
     * 根据紧急程度查询用工信息（公开接口）
     */
    @GetMapping("/urgency/{urgencyLevel}")
    public TableDataInfo getByUrgency(@PathVariable("urgencyLevel") String urgencyLevel)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByUrgency(urgencyLevel);
        return getDataTable(list);
    }

    /**
     * 根据关键词搜索用工信息（公开接口）
     */
    @GetMapping("/search/{keyword}")
    public TableDataInfo searchByKeyword(@PathVariable("keyword") String keyword)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 获取即将到期的用工信息（公开接口）
     */
    @GetMapping("/expiring")
    public AjaxResult getExpiringSoon(Integer days)
    {
        if (days == null) days = 7; // 默认7天内到期
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoExpiringSoon(days);
        return success(list);
    }

    /**
     * 获取所有用工类型（公开接口）
     */
    @GetMapping("/types")
    public AjaxResult getAllTypes()
    {
        List<String> types = employmentInfoService.selectAllEmploymentTypes();
        return success(types);
    }

    /**
     * 获取所有工作类别（公开接口）
     */
    @GetMapping("/categories")
    public AjaxResult getAllCategories()
    {
        List<String> categories = employmentInfoService.selectAllWorkCategories();
        return success(categories);
    }

    /**
     * 获取所有薪资类型（公开接口）
     */
    @GetMapping("/salaryTypes")
    public AjaxResult getAllSalaryTypes()
    {
        List<String> salaryTypes = employmentInfoService.selectAllSalaryTypes();
        return success(salaryTypes);
    }

    /**
     * 获取所有区域信息（公开接口）
     */
    @GetMapping("/regions")
    public AjaxResult getAllRegions()
    {
        List<Map<String, Object>> regions = employmentInfoService.selectAllRegions();
        return success(regions);
    }

    /**
     * 获取所有学历要求（公开接口）
     */
    @GetMapping("/educations")
    public AjaxResult getAllEducations()
    {
        List<String> educations = employmentInfoService.selectAllEducationRequirements();
        return success(educations);
    }

    /**
     * 根据核心字段查询用工信息（公开接口）
     */
    @GetMapping("/filter")
    public TableDataInfo filterByCoreFields(String employmentType, String workCategory, String salaryType, String regionCode, String keyword)
    {
        startPage();
        List<EmploymentInfo> list = employmentInfoService.selectEmploymentInfoByCoreFields(employmentType, workCategory, salaryType, regionCode, keyword);
        return getDataTable(list);
    }
}

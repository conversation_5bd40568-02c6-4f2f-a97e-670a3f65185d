package com.sux.web.controller.public;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.PlaceInfo;
import com.sux.system.service.IPlaceInfoService;

/**
 * 场地信息公开接口Controller
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/place")
public class PublicPlaceController extends BaseController
{
    @Autowired
    private IPlaceInfoService placeInfoService;

    /**
     * 查询场地信息列表（公开接口）
     */
    @GetMapping("/list")
    public TableDataInfo list(PlaceInfo placeInfo)
    {
        startPage();
        // 只查询正常状态的场地
        placeInfo.setStatus("0");
        List<PlaceInfo> list = placeInfoService.selectPublishedPlaceInfoList(placeInfo);
        return getDataTable(list);
    }

    /**
     * 获取场地信息详细信息（公开接口）
     */
    @GetMapping(value = "/{placeId}")
    public AjaxResult getInfo(@PathVariable("placeId") Long placeId)
    {
        PlaceInfo placeInfo = placeInfoService.selectPlaceInfoByPlaceId(placeId);
        if (placeInfo != null && "0".equals(placeInfo.getStatus())) {
            // 增加浏览次数
            placeInfoService.updatePlaceInfoViewCount(placeId);
            return success(placeInfo);
        }
        return error("场地信息不存在或已下线");
    }

    /**
     * 获取场地统计信息（公开接口）
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> statistics = placeInfoService.selectPlaceInfoStatistics();
        return success(statistics);
    }

    /**
     * 获取推荐场地列表（公开接口）
     */
    @GetMapping("/featured")
    public AjaxResult getFeaturedList()
    {
        PlaceInfo placeInfo = new PlaceInfo();
        placeInfo.setStatus("0");
        placeInfo.setIsFeatured(1);
        List<PlaceInfo> list = placeInfoService.selectFeaturedPlaceInfoList(placeInfo);
        return success(list);
    }

    /**
     * 根据场地类型查询场地信息（公开接口）
     */
    @GetMapping("/type/{placeType}")
    public TableDataInfo getByType(@PathVariable("placeType") String placeType)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByType(placeType);
        return getDataTable(list);
    }

    /**
     * 根据区域查询场地信息（公开接口）
     */
    @GetMapping("/region/{regionCode}")
    public TableDataInfo getByRegion(@PathVariable("regionCode") String regionCode)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByRegion(regionCode);
        return getDataTable(list);
    }

    /**
     * 根据关键词搜索场地信息（公开接口）
     */
    @GetMapping("/search/{keyword}")
    public TableDataInfo searchByKeyword(@PathVariable("keyword") String keyword)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 获取所有场地类型（公开接口）
     */
    @GetMapping("/types")
    public AjaxResult getAllTypes()
    {
        List<String> types = placeInfoService.selectAllPlaceTypes();
        return success(types);
    }

    /**
     * 获取所有区域信息（公开接口）
     */
    @GetMapping("/regions")
    public AjaxResult getAllRegions()
    {
        List<Map<String, Object>> regions = placeInfoService.selectAllRegions();
        return success(regions);
    }

    /**
     * 获取所有场地等级（公开接口）
     */
    @GetMapping("/levels")
    public AjaxResult getAllLevels()
    {
        List<String> levels = placeInfoService.selectAllPlaceLevels();
        return success(levels);
    }

    /**
     * 获取所有行业方向（公开接口）
     */
    @GetMapping("/industries")
    public AjaxResult getAllIndustries()
    {
        List<String> industries = placeInfoService.selectAllIndustryDirections();
        return success(industries);
    }

    /**
     * 获取所有运营模式（公开接口）
     */
    @GetMapping("/operations")
    public AjaxResult getAllOperations()
    {
        List<String> operations = placeInfoService.selectAllOperationModes();
        return success(operations);
    }

    /**
     * 根据核心字段查询场地信息（公开接口）
     */
    @GetMapping("/filter")
    public TableDataInfo filterByCoreFields(String placeType, String placeLevel, String regionCode, String industryDirection, String operationMode, String keyword)
    {
        startPage();
        List<PlaceInfo> list = placeInfoService.selectPlaceInfoByCoreFields(placeType, placeLevel, regionCode, industryDirection, operationMode, keyword);
        return getDataTable(list);
    }
}

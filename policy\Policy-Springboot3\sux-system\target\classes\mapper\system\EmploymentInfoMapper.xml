<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.EmploymentInfoMapper">

    <resultMap type="EmploymentInfo" id="EmploymentInfoResult">
        <result property="employmentId"             column="employment_id"             />
        <result property="title"                    column="title"                     />
        <result property="employmentType"           column="employment_type"           />
        <result property="workCategory"             column="work_category"             />
        <result property="workLocation"             column="work_location"             />
        <result property="regionCode"               column="region_code"               />
        <result property="regionName"               column="region_name"               />
        <result property="salaryType"               column="salary_type"               />
        <result property="salaryMin"                column="salary_min"                />
        <result property="salaryMax"                column="salary_max"                />
        <result property="workHoursPerDay"          column="work_hours_per_day"        />
        <result property="workDaysPerWeek"          column="work_days_per_week"        />
        <result property="startDate"                column="start_date"                />
        <result property="endDate"                  column="end_date"                  />
        <result property="positionsNeeded"          column="positions_needed"          />
        <result property="positionsFilled"          column="positions_filled"          />
        <result property="educationRequired"        column="education_required"        />
        <result property="experienceRequired"       column="experience_required"       />
        <result property="ageMin"                   column="age_min"                   />
        <result property="ageMax"                   column="age_max"                   />
        <result property="genderRequired"           column="gender_required"           />
        <result property="skillsRequired"           column="skills_required"           />
        <result property="workDescription"          column="work_description"          />
        <result property="welfareBenefits"          column="welfare_benefits"          />
        <result property="contactPerson"            column="contact_person"            />
        <result property="contactPhone"             column="contact_phone"             />
        <result property="contactEmail"             column="contact_email"             />
        <result property="companyName"              column="company_name"              />
        <result property="companyAddress"           column="company_address"           />
        <result property="companyDescription"       column="company_description"       />
        <result property="urgencyLevel"             column="urgency_level"             />
        <result property="applicationDeadline"      column="application_deadline"      />
        <result property="status"                   column="status"                    />
        <result property="isVerified"               column="is_verified"               />
        <result property="isFeatured"               column="is_featured"               />
        <result property="viewCount"                column="view_count"                />
        <result property="applicationCount"         column="application_count"         />
        <result property="publisherUserId"          column="publisher_user_id"         />
        <result property="createId"                 column="create_id"                 />
        <result property="createTime"               column="create_time"               />
        <result property="updateId"                 column="update_id"                 />
        <result property="updateTime"               column="update_time"               />
        <result property="delFlag"                  column="del_flag"                  />
        <result property="remark"                   column="remark"                    />
    </resultMap>

    <sql id="selectEmploymentInfoVo">
        select employment_id, title, employment_type, work_category, work_location, region_code, region_name,
               salary_type, salary_min, salary_max, work_hours_per_day, work_days_per_week, start_date, end_date,
               positions_needed, positions_filled, education_required, experience_required, age_min, age_max,
               gender_required, skills_required, work_description, welfare_benefits, contact_person, contact_phone,
               contact_email, company_name, company_address, company_description, urgency_level, application_deadline,
               status, is_verified, is_featured, view_count, application_count, publisher_user_id,
               create_id, create_time, update_id, update_time, del_flag, remark
        from employment_info
    </sql>

    <select id="selectEmploymentInfoList" parameterType="EmploymentInfo" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        <where>
            del_flag = '0'
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="employmentType != null and employmentType != ''"> and employment_type = #{employmentType}</if>
            <if test="workCategory != null and workCategory != ''"> and work_category = #{workCategory}</if>
            <if test="workLocation != null and workLocation != ''"> and work_location like concat('%', #{workLocation}, '%')</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="regionName != null and regionName != ''"> and region_name like concat('%', #{regionName}, '%')</if>
            <if test="salaryType != null and salaryType != ''"> and salary_type = #{salaryType}</if>
            <if test="companyName != null and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="urgencyLevel != null and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="isVerified != null"> and is_verified = #{isVerified}</if>
            <if test="isFeatured != null"> and is_featured = #{isFeatured}</if>
            <if test="publisherUserId != null"> and publisher_user_id = #{publisherUserId}</if>
        </where>
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectEmploymentInfoByEmploymentId" parameterType="Long" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where employment_id = #{employmentId} and del_flag = '0'
    </select>

    <insert id="insertEmploymentInfo" parameterType="EmploymentInfo" useGeneratedKeys="true" keyProperty="employmentId">
        insert into employment_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="employmentType != null and employmentType != ''">employment_type,</if>
            <if test="workCategory != null and workCategory != ''">work_category,</if>
            <if test="workLocation != null and workLocation != ''">work_location,</if>
            <if test="regionCode != null and regionCode != ''">region_code,</if>
            <if test="regionName != null and regionName != ''">region_name,</if>
            <if test="salaryType != null and salaryType != ''">salary_type,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="workHoursPerDay != null">work_hours_per_day,</if>
            <if test="workDaysPerWeek != null">work_days_per_week,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="positionsNeeded != null">positions_needed,</if>
            <if test="positionsFilled != null">positions_filled,</if>
            <if test="educationRequired != null and educationRequired != ''">education_required,</if>
            <if test="experienceRequired != null and experienceRequired != ''">experience_required,</if>
            <if test="ageMin != null">age_min,</if>
            <if test="ageMax != null">age_max,</if>
            <if test="genderRequired != null and genderRequired != ''">gender_required,</if>
            <if test="skillsRequired != null">skills_required,</if>
            <if test="workDescription != null">work_description,</if>
            <if test="welfareBenefits != null">welfare_benefits,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="companyAddress != null and companyAddress != ''">company_address,</if>
            <if test="companyDescription != null">company_description,</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level,</if>
            <if test="applicationDeadline != null">application_deadline,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="isVerified != null">is_verified,</if>
            <if test="isFeatured != null">is_featured,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="applicationCount != null">application_count,</if>
            <if test="publisherUserId != null">publisher_user_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="employmentType != null and employmentType != ''">#{employmentType},</if>
            <if test="workCategory != null and workCategory != ''">#{workCategory},</if>
            <if test="workLocation != null and workLocation != ''">#{workLocation},</if>
            <if test="regionCode != null and regionCode != ''">#{regionCode},</if>
            <if test="regionName != null and regionName != ''">#{regionName},</if>
            <if test="salaryType != null and salaryType != ''">#{salaryType},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="workHoursPerDay != null">#{workHoursPerDay},</if>
            <if test="workDaysPerWeek != null">#{workDaysPerWeek},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="positionsNeeded != null">#{positionsNeeded},</if>
            <if test="positionsFilled != null">#{positionsFilled},</if>
            <if test="educationRequired != null and educationRequired != ''">#{educationRequired},</if>
            <if test="experienceRequired != null and experienceRequired != ''">#{experienceRequired},</if>
            <if test="ageMin != null">#{ageMin},</if>
            <if test="ageMax != null">#{ageMax},</if>
            <if test="genderRequired != null and genderRequired != ''">#{genderRequired},</if>
            <if test="skillsRequired != null">#{skillsRequired},</if>
            <if test="workDescription != null">#{workDescription},</if>
            <if test="welfareBenefits != null">#{welfareBenefits},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">#{contactEmail},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="companyAddress != null and companyAddress != ''">#{companyAddress},</if>
            <if test="companyDescription != null">#{companyDescription},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">#{urgencyLevel},</if>
            <if test="applicationDeadline != null">#{applicationDeadline},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="isVerified != null">#{isVerified},</if>
            <if test="isFeatured != null">#{isFeatured},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="applicationCount != null">#{applicationCount},</if>
            <if test="publisherUserId != null">#{publisherUserId},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
        </trim>
    </insert>

    <update id="updateEmploymentInfo" parameterType="EmploymentInfo">
        update employment_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="employmentType != null and employmentType != ''">employment_type = #{employmentType},</if>
            <if test="workCategory != null and workCategory != ''">work_category = #{workCategory},</if>
            <if test="workLocation != null and workLocation != ''">work_location = #{workLocation},</if>
            <if test="regionCode != null and regionCode != ''">region_code = #{regionCode},</if>
            <if test="regionName != null and regionName != ''">region_name = #{regionName},</if>
            <if test="salaryType != null and salaryType != ''">salary_type = #{salaryType},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="workHoursPerDay != null">work_hours_per_day = #{workHoursPerDay},</if>
            <if test="workDaysPerWeek != null">work_days_per_week = #{workDaysPerWeek},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="positionsNeeded != null">positions_needed = #{positionsNeeded},</if>
            <if test="positionsFilled != null">positions_filled = #{positionsFilled},</if>
            <if test="educationRequired != null and educationRequired != ''">education_required = #{educationRequired},</if>
            <if test="experienceRequired != null and experienceRequired != ''">experience_required = #{experienceRequired},</if>
            <if test="ageMin != null">age_min = #{ageMin},</if>
            <if test="ageMax != null">age_max = #{ageMax},</if>
            <if test="genderRequired != null and genderRequired != ''">gender_required = #{genderRequired},</if>
            <if test="skillsRequired != null">skills_required = #{skillsRequired},</if>
            <if test="workDescription != null">work_description = #{workDescription},</if>
            <if test="welfareBenefits != null">welfare_benefits = #{welfareBenefits},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null and contactEmail != ''">contact_email = #{contactEmail},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="companyAddress != null and companyAddress != ''">company_address = #{companyAddress},</if>
            <if test="companyDescription != null">company_description = #{companyDescription},</if>
            <if test="urgencyLevel != null and urgencyLevel != ''">urgency_level = #{urgencyLevel},</if>
            <if test="applicationDeadline != null">application_deadline = #{applicationDeadline},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="isVerified != null">is_verified = #{isVerified},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="applicationCount != null">application_count = #{applicationCount},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where employment_id = #{employmentId}
    </update>

    <delete id="deleteEmploymentInfoByEmploymentId" parameterType="Long">
        update employment_info set del_flag = '2' where employment_id = #{employmentId}
    </delete>

    <delete id="deleteEmploymentInfoByEmploymentIds" parameterType="String">
        update employment_info set del_flag = '2' where employment_id in
        <foreach item="employmentId" collection="array" open="(" separator="," close=")">
            #{employmentId}
        </foreach>
    </delete>

    <select id="selectPublishedEmploymentInfoList" parameterType="EmploymentInfo" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        <where>
            del_flag = '0' and status = 'published'
            <if test="employmentType != null and employmentType != ''"> and employment_type = #{employmentType}</if>
            <if test="workCategory != null and workCategory != ''"> and work_category = #{workCategory}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="salaryType != null and salaryType != ''"> and salary_type = #{salaryType}</if>
        </where>
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectFeaturedEmploymentInfoList" parameterType="EmploymentInfo" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        <where>
            del_flag = '0' and status = 'published' and is_featured = 1
            <if test="employmentType != null and employmentType != ''"> and employment_type = #{employmentType}</if>
            <if test="workCategory != null and workCategory != ''"> and work_category = #{workCategory}</if>
            <if test="regionCode != null and regionCode != ''"> and region_code = #{regionCode}</if>
        </where>
        order by urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectMyEmploymentInfoList" parameterType="EmploymentInfo" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        <where>
            del_flag = '0' and publisher_user_id = #{publisherUserId}
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="title != null and title != ''"> and title like concat('%', #{title}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectEmploymentInfoByType" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and employment_type = #{employmentType}
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectEmploymentInfoByCategory" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and work_category = #{workCategory}
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectEmploymentInfoByRegion" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and region_code = #{regionCode}
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectEmploymentInfoBySalaryType" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and salary_type = #{salaryType}
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <select id="selectEmploymentInfoBySalaryRange" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published'
        <if test="param1 != null"> and salary_min >= #{param1}</if>
        <if test="param2 != null"> and salary_max <= #{param2}</if>
        order by is_featured desc, salary_min desc, create_time desc
    </select>

    <select id="selectEmploymentInfoByUrgency" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and urgency_level = #{urgencyLevel}
        order by is_featured desc, create_time desc
    </select>

    <select id="selectEmploymentInfoStatistics" resultType="Map">
        select
            count(*) as total_count,
            sum(case when status = 'published' then 1 else 0 end) as published_count,
            sum(case when status = 'draft' then 1 else 0 end) as draft_count,
            sum(case when is_featured = 1 then 1 else 0 end) as featured_count,
            sum(case when urgency_level = 'urgent' then 1 else 0 end) as urgent_count,
            sum(positions_needed) as total_positions_needed,
            sum(positions_filled) as total_positions_filled,
            sum(view_count) as total_view_count,
            sum(application_count) as total_application_count
        from employment_info
        where del_flag = '0'
    </select>

    <select id="selectEmploymentInfoByKeyword" parameterType="String" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published'
        and (title like concat('%', #{keyword}, '%')
             or work_category like concat('%', #{keyword}, '%')
             or work_location like concat('%', #{keyword}, '%')
             or company_name like concat('%', #{keyword}, '%')
             or work_description like concat('%', #{keyword}, '%'))
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

    <update id="updateEmploymentInfoViewCount" parameterType="Long">
        update employment_info set view_count = view_count + 1 where employment_id = #{employmentId}
    </update>

    <update id="updateEmploymentInfoApplicationCount" parameterType="Long">
        update employment_info set application_count = application_count + 1 where employment_id = #{employmentId}
    </update>

    <select id="selectEmploymentInfoExpiringSoon" parameterType="Integer" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published'
        and application_deadline is not null
        and application_deadline between now() and date_add(now(), interval #{days} day)
        order by application_deadline asc
    </select>

    <select id="selectEmploymentInfoDetailByEmploymentId" parameterType="Long" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where employment_id = #{employmentId} and del_flag = '0'
    </select>

    <select id="selectSimilarEmploymentInfoList" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        where del_flag = '0' and status = 'published' and employment_id != #{employmentInfo.employmentId}
        <if test="employmentInfo.workCategory != null and employmentInfo.workCategory != ''">
            and work_category = #{employmentInfo.workCategory}
        </if>
        <if test="employmentInfo.regionCode != null and employmentInfo.regionCode != ''">
            and region_code = #{employmentInfo.regionCode}
        </if>
        <if test="employmentInfo.salaryType != null and employmentInfo.salaryType != ''">
            and salary_type = #{employmentInfo.salaryType}
        </if>
        order by is_featured desc, create_time desc
        limit #{limit}
    </select>

    <select id="selectAllEmploymentTypes" resultType="String">
        select distinct employment_type from employment_info
        where del_flag = '0' and employment_type is not null and employment_type != ''
        order by employment_type
    </select>

    <select id="selectAllWorkCategories" resultType="String">
        select distinct work_category from employment_info
        where del_flag = '0' and work_category is not null and work_category != ''
        order by work_category
    </select>

    <select id="selectAllSalaryTypes" resultType="String">
        select distinct salary_type from employment_info
        where del_flag = '0' and salary_type is not null and salary_type != ''
        order by salary_type
    </select>

    <select id="selectAllRegions" resultType="Map">
        select distinct region_code as regionCode, region_name as regionName from employment_info
        where del_flag = '0' and region_code is not null and region_code != ''
        order by region_code
    </select>

    <select id="selectAllEducationRequirements" resultType="String">
        select distinct education_required from employment_info
        where del_flag = '0' and education_required is not null and education_required != ''
        order by education_required
    </select>

    <select id="selectEmploymentInfoByCoreFields" resultMap="EmploymentInfoResult">
        <include refid="selectEmploymentInfoVo"/>
        <where>
            del_flag = '0' and status = 'published'
            <if test="param1 != null and param1 != ''"> and employment_type = #{param1}</if>
            <if test="param2 != null and param2 != ''"> and work_category = #{param2}</if>
            <if test="param3 != null and param3 != ''"> and salary_type = #{param3}</if>
            <if test="param4 != null and param4 != ''"> and region_code = #{param4}</if>
            <if test="param5 != null and param5 != ''"> and title like concat('%', #{param5}, '%')</if>
        </where>
        order by is_featured desc, urgency_level = 'urgent' desc, create_time desc
    </select>

</mapper>
